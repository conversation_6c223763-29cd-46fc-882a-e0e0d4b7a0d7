---
description: 
globs: 
alwaysApply: true
---
# TAM AAP Backend Development Rules

## Project Structure
- Follow the established directory structure:
  - `/src/controllers/` - Request handlers
  - `/src/services/` - Business logic and external service calls
  - `/src/routes/` - Route definitions
  - `/src/schemas/` - TypeBox schemas for validation and documentation
  - `/src/transformers/` - Data transformation logic
  - `/src/types/` - TypeScript type definitions
  - `/src/configs/` - Configuration files

## New Endpoint Development
When implementing a new endpoint (e.g., dataset endpoint), follow these steps:

1. **Reference Similar Endpoints**
   - Always look at similar existing endpoints (e.g., metrics, glossary) for patterns
   - Study their implementation in all layers (controller, service, routes, schemas, transformers)
   - Maintain consistency in naming conventions and structure

2. **Schema Definition** (`/src/schemas/`)
   - Create a new schema file (e.g., `datasetSchema.ts`)
   - Define query parameters, request body, and response schemas using TypeBox
   - Include comprehensive descriptions and examples for Swagger documentation
   - Follow the pattern of separating validation schemas from Swagger schemas

3. **Service Layer** (`/src/services/`)
   - Create a service file (e.g., `datasetService.ts`)
   - Implement business logic and external service calls
   - Use axios for HTTP requests
   - Follow error handling patterns from existing services
   - Use configuration from `/src/configs/configs.ts`

4. **Controller Layer** (`/src/controllers/`)
   - Create a controller file (e.g., `datasetController.ts`)
   - Implement request handlers with proper typing
   - Use FastifyRequest and FastifyReply types
   - Follow error handling patterns
   - Use transformers for response formatting

5. **Transformer Layer** (`/src/transformers/`)
   - Create a transformer file (e.g., `datasetTransformer.ts`)
   - Define interfaces for raw and transformed data
   - Implement transformation functions
   - Handle custom properties consistently

6. **Route Definition** (`/src/routes/`)
   - Create a route file (e.g., `datasetRoutes.ts`)
   - Use FastifyPluginAsync for route definition
   - Apply schemas for validation and documentation
   - Register routes in `app.ts`

## Code Style Guidelines
1. **TypeScript Best Practices**
   - Use strict typing
   - Define interfaces for all data structures
   - Use TypeBox for runtime validation
   - Export types when needed across files

2. **Error Handling**
   - Use try-catch blocks in controllers
   - Log errors using request.server.log
   - Return appropriate HTTP status codes
   - Provide meaningful error messages

3. **Documentation**
   - Add comprehensive JSDoc comments
   - Include examples in Swagger documentation
   - Document all query parameters and response fields
   - Keep README.md updated

4. **Testing**
   - Write unit tests for new functionality
   - Follow existing test patterns
   - Test error cases and edge conditions

## Integration Guidelines
1. **External Services**
   - Use configuration from `configs.ts`
   - Handle timeouts and retries
   - Implement proper error handling
   - Use environment variables for sensitive data

2. **API Design**
   - Follow RESTful principles
   - Use consistent URL patterns
   - Implement proper pagination
   - Support filtering and search functionality

## Example Implementation Pattern
When implementing a new endpoint (e.g., dataset):

1. First, examine similar endpoints:
   ```typescript
   // Look at metrics implementation
   /src/controllers/metricsController.ts
   /src/services/metricsService.ts
   /src/routes/metricsRoutes.ts
   /src/schemas/metricsSchema.ts
   /src/transformers/metricsTransformer.ts
   ```

2. Follow the same pattern for your new endpoint:
   ```typescript
   // Your new dataset implementation
   /src/controllers/datasetController.ts
   /src/services/datasetService.ts
   /src/routes/datasetRoutes.ts
   /src/schemas/datasetSchema.ts
   /src/transformers/datasetTransformer.ts
   ```

3. Register the new routes in `app.ts`

## Best Practices
1. Always check existing implementations for patterns
2. Maintain consistent error handling
3. Use proper typing and validation
4. Document all new endpoints
5. Follow the established project structure
6. Keep code modular and maintainable
7. Use proper logging and monitoring
8. Implement proper security measures 