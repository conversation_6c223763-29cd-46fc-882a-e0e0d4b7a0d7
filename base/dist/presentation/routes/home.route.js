import { HomeService } from '../../application/services/HomeService.js';
import { BaseRoute } from './base.route.js';
import { indexResponseSchema } from '../schemas/home.schema.js';
export class HomeRoute extends BaseRoute {
    constructor(fastify, version) {
        super(fastify, version);
    }
    async register() {
        const homeService = new HomeService();
        this.fastify.get(`${this.prefix}/`, {
            schema: indexResponseSchema,
            handler: async (request, reply) => {
                const home = homeService.getHomeMessage();
                return {
                    message: home.getMessage(),
                    timestamp: home.getTimestamp().toISOString(),
                };
            },
        });
    }
}
