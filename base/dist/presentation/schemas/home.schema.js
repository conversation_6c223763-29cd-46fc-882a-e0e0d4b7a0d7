export const indexResponseSchema = {
    description: 'Home endpoint that returns a greeting message',
    tags: ['Home'],
    summary: 'Get home message',
    operationId: 'getHome',
    responses: {
        200: {
            description: 'Successful response',
            content: {
                'application/json': {
                    schema: {
                        type: 'object',
                        required: ['message', 'timestamp'],
                        properties: {
                            message: {
                                type: 'string',
                                description: 'Home message',
                                example: 'Welcome to TAM AAP Integration Hub',
                            },
                            timestamp: {
                                type: 'string',
                                format: 'date-time',
                                description: 'ISO timestamp of when the message was generated',
                                example: '2024-03-06T12:00:00Z',
                            },
                        },
                    },
                },
            },
        },
    },
};
