import fastify from 'fastify';
import cors from '@fastify/cors';
import helmet from '@fastify/helmet';
import swagger from '@fastify/swagger';
import swaggerUi from '@fastify/swagger-ui';
import { corsOptions, helmetOptions } from '../config/security.config.js';
import { swaggerOptions, swaggerUiOptions } from '../config/swagger.config.js';
import { HomeRoute } from '../../presentation/routes/home.route.js';
import { DEFAULT_API_VERSION } from '../config/api.config.js';
export async function buildApp() {
    const app = fastify({
        logger: true,
        trustProxy: true,
    });
    // Register security plugins
    await app.register(helmet, helmetOptions);
    await app.register(cors, corsOptions);
    // Register Swagger
    await app.register(swagger, swaggerOptions);
    await app.register(swaggerUi, swaggerUiOptions);
    // Register only the default versioned home route
    const activeHomeRoute = new HomeRoute(app, DEFAULT_API_VERSION);
    await activeHomeRoute.register();
    return app;
}
