import { config } from './index.js';
import { DEFAULT_API_VERSION } from './api.config.js';
export const swaggerOptions = {
    openapi: {
        info: {
            title: 'TAM AAP Integration Hub API',
            description: 'API documentation for TAM AAP Integration Hub',
            version: '1.0.0',
            contact: {
                name: 'TAM AAP Integration Hub Team',
            },
        },
        servers: [
            {
                url: `http://localhost:${config.port}/api/${DEFAULT_API_VERSION}`,
                description: `Development server - ${DEFAULT_API_VERSION.toUpperCase()}`,
            },
        ],
        components: {
            securitySchemes: {
                bearerAuth: {
                    type: 'http',
                    scheme: 'bearer',
                    bearerFormat: 'JWT',
                },
            },
        },
        tags: [{ name: 'Welcome', description: 'Welcome endpoints' }],
    },
};
export const swaggerUiOptions = {
    routePrefix: '/docs',
    uiConfig: {
        docExpansion: 'list',
        deepLinking: false,
    },
    staticCSP: true,
    transformStaticCSP: (header) => header,
};
