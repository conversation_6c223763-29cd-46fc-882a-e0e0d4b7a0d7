import { buildApp } from './infrastructure/server/app.js';
import { config } from './infrastructure/config/index.js';
async function start() {
    try {
        const app = await buildApp();
        // Start the server
        await app.listen({
            port: Number(config.port),
            host: '0.0.0.0',
        });
        console.log(`Server is running on port ${config.port}`);
    }
    catch (err) {
        console.error('Error starting server:', err);
        process.exit(1);
    }
}
start();
