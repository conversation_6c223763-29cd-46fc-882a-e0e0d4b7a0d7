{"version": 3, "names": ["_index", "require", "appendToMemberExpression", "member", "append", "computed", "object", "memberExpression", "property"], "sources": ["../../src/modifications/appendToMemberExpression.ts"], "sourcesContent": ["import { memberExpression } from \"../builders/generated/index.ts\";\nimport type * as t from \"../index.ts\";\n\n/**\n * Append a node to a member expression.\n */\nexport default function appendToMemberExpression(\n  member: t.MemberExpression,\n  append: t.MemberExpression[\"property\"],\n  computed: boolean = false,\n): t.MemberExpression {\n  member.object = memberExpression(\n    member.object,\n    member.property,\n    member.computed,\n  );\n  member.property = append;\n  member.computed = !!computed;\n\n  return member;\n}\n"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAMe,SAASC,wBAAwBA,CAC9CC,MAA0B,EAC1BC,MAAsC,EACtCC,QAAiB,GAAG,KAAK,EACL;EACpBF,MAAM,CAACG,MAAM,GAAG,IAAAC,uBAAgB,EAC9BJ,MAAM,CAACG,MAAM,EACbH,MAAM,CAACK,QAAQ,EACfL,MAAM,CAACE,QACT,CAAC;EACDF,MAAM,CAACK,QAAQ,GAAGJ,MAAM;EACxBD,MAAM,CAACE,QAAQ,GAAG,CAAC,CAACA,QAAQ;EAE5B,OAAOF,MAAM;AACf", "ignoreList": []}