{"version": 3, "names": ["_path", "data", "require", "_helperCompilationTargets", "resolveBrowserslistConfigFile", "browserslistConfigFile", "configFileDir", "path", "resolve", "resolveTargets", "options", "root", "optTargets", "targets", "Array", "isArray", "browsers", "Object", "assign", "<PERSON><PERSON><PERSON><PERSON>", "configFile", "ignoreBrowserslistConfig", "getTargets", "config<PERSON><PERSON>", "browserslistEnv"], "sources": ["../../src/config/resolve-targets.ts"], "sourcesContent": ["type browserType = typeof import(\"./resolve-targets-browser\");\ntype nodeType = typeof import(\"./resolve-targets\");\n\n// Kind of gross, but essentially asserting that the exports of this module are the same as the\n// exports of index-browser, since this file may be replaced at bundle time with index-browser.\n({}) as any as browserType as nodeType;\n\nimport type { ValidatedOptions } from \"./validation/options.ts\";\nimport path from \"node:path\";\nimport getTargets, {\n  type InputTargets,\n} from \"@babel/helper-compilation-targets\";\n\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nexport function resolveBrowserslistConfigFile(\n  browserslistConfigFile: string,\n  configFileDir: string,\n): string | undefined {\n  return path.resolve(configFileDir, browserslistConfigFile);\n}\n\nexport function resolveTargets(\n  options: ValidatedOptions,\n  root: string,\n): Targets {\n  const optTargets = options.targets;\n  let targets: InputTargets;\n\n  if (typeof optTargets === \"string\" || Array.isArray(optTargets)) {\n    targets = { browsers: optTargets };\n  } else if (optTargets) {\n    if (\"esmodules\" in optTargets) {\n      targets = { ...optTargets, esmodules: \"intersect\" };\n    } else {\n      // https://github.com/microsoft/TypeScript/issues/17002\n      targets = optTargets as InputTargets;\n    }\n  }\n\n  const { browserslistConfigFile } = options;\n  let configFile;\n  let ignoreBrowserslistConfig = false;\n  if (typeof browserslistConfigFile === \"string\") {\n    configFile = browserslistConfigFile;\n  } else {\n    ignoreBrowserslistConfig = browserslistConfigFile === false;\n  }\n\n  return getTargets(targets, {\n    ignoreBrowserslistConfig,\n    configFile,\n    configPath: root,\n    browserslistEnv: options.browserslistEnv,\n  });\n}\n"], "mappings": ";;;;;;;AAQA,SAAAA,MAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,KAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AACA,SAAAE,0BAAA;EAAA,MAAAF,IAAA,GAAAC,OAAA;EAAAC,yBAAA,YAAAA,CAAA;IAAA,OAAAF,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAJA,CAAC,CAAC,CAAC;AAUI,SAASG,6BAA6BA,CAC3CC,sBAA8B,EAC9BC,aAAqB,EACD;EACpB,OAAOC,MAAGA,CAAC,CAACC,OAAO,CAACF,aAAa,EAAED,sBAAsB,CAAC;AAC5D;AAEO,SAASI,cAAcA,CAC5BC,OAAyB,EACzBC,IAAY,EACH;EACT,MAAMC,UAAU,GAAGF,OAAO,CAACG,OAAO;EAClC,IAAIA,OAAqB;EAEzB,IAAI,OAAOD,UAAU,KAAK,QAAQ,IAAIE,KAAK,CAACC,OAAO,CAACH,UAAU,CAAC,EAAE;IAC/DC,OAAO,GAAG;MAAEG,QAAQ,EAAEJ;IAAW,CAAC;EACpC,CAAC,MAAM,IAAIA,UAAU,EAAE;IACrB,IAAI,WAAW,IAAIA,UAAU,EAAE;MAC7BC,OAAO,GAAAI,MAAA,CAAAC,MAAA,KAAQN,UAAU;QAAEO,SAAS,EAAE;MAAW,EAAE;IACrD,CAAC,MAAM;MAELN,OAAO,GAAGD,UAA0B;IACtC;EACF;EAEA,MAAM;IAAEP;EAAuB,CAAC,GAAGK,OAAO;EAC1C,IAAIU,UAAU;EACd,IAAIC,wBAAwB,GAAG,KAAK;EACpC,IAAI,OAAOhB,sBAAsB,KAAK,QAAQ,EAAE;IAC9Ce,UAAU,GAAGf,sBAAsB;EACrC,CAAC,MAAM;IACLgB,wBAAwB,GAAGhB,sBAAsB,KAAK,KAAK;EAC7D;EAEA,OAAO,IAAAiB,mCAAU,EAACT,OAAO,EAAE;IACzBQ,wBAAwB;IACxBD,UAAU;IACVG,UAAU,EAAEZ,IAAI;IAChBa,eAAe,EAAEd,OAAO,CAACc;EAC3B,CAAC,CAAC;AACJ;AAAC", "ignoreList": []}