replicaCount: 1

image:
  repository: 692859917251.dkr.ecr.eu-north-1.amazonaws.com/tam-aap-frontend
  tag: v1.1.1

# Configure ingress to use the LoadBalancer hostname
ingress:
  enabled: true
  className: nginx
  annotations: {}
  hosts:
    - host: a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com
      paths:
        - path: /
          pathType: Prefix
  tls: []

env:
  NODE_ENV: production
  PORT: 3000
  # NEXTAUTH_URL must match the ingress host exactly
  NEXTAUTH_URL: http://a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com
  NEXTAUTH_SECRET: your-nextauth-secret-key-here
  KEYCLOAK_CLIENT_ID: tam-aap-frontend
  KEYCLOAK_CLIENT_SECRET: DogFEyi5TSU5JtBPCKe94cU4wgdvrsj<PERSON>_ISSUER: http://a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com/auth/realms/aap-tam
  BACKEND_URL: http://airport-acumen-backend.aap-tam:3020
  # Force single replica to avoid state cookie issues
  NEXTAUTH_COOKIE_SECURE: false
  NEXTAUTH_COOKIE_SAMESITE: lax
