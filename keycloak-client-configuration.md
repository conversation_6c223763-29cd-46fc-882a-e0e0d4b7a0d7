# Keycloak Client Configuration for tam-aap-frontend

## Required Keycloak Client Settings

To fix the "Invalid parameter: redirect_uri" error, you need to configure the Keycloak client `tam-aap-frontend` in the `aap-tam` realm with the following settings:

### Client Configuration

1. **Client ID**: `tam-aap-frontend`
2. **Client Type**: `OpenID Connect`
3. **Access Type**: `confidential`

### Valid Redirect URIs

Add the following redirect URIs to the client configuration:

```
http://a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com/api/auth/callback/keycloak
http://a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com/*
```

### Web Origins

Add the following web origins:

```
http://a1b3afed4e23a47e3be07037a5967a09-a09c81d94aed5efe.elb.eu-north-1.amazonaws.com
```

### Steps to Configure in Keycloak Admin Console

1. Log into Keycloak Admin Console
2. Select the `aap-tam` realm
3. Go to **Clients** → **tam-aap-frontend**
4. In the **Settings** tab:
   - Set **Access Type** to `confidential`
   - Add the redirect URIs listed above to **Valid Redirect URIs**
   - Add the web origin listed above to **Web Origins**
   - Set **Standard Flow Enabled** to `ON`
   - Set **Direct Access Grants Enabled** to `ON` (optional, for testing)
5. In the **Credentials** tab:
   - Note the **Secret** value (should match `KEYCLOAK_CLIENT_SECRET` in your values)
6. Save the configuration

### Important Notes

- The redirect URI must match **exactly** what NextAuth.js sends
- The LoadBalancer URL should be consistent across all configurations
- If you change the LoadBalancer URL, update both the Helm values and Keycloak client configuration
